{".class": "MypyFile", "_fullname": "ebm_integration_guide", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef"}, "Dict": {".class": "SymbolTableNode", "cross_ref": "typing.Dict", "kind": "Gdef"}, "EBMContentOptimizer": {".class": "SymbolTableNode", "cross_ref": "ebm_content_optimizer.EBMContentOptimizer", "kind": "Gdef"}, "EBMSection": {".class": "SymbolTableNode", "cross_ref": "ebm_content_optimizer.EBMSection", "kind": "Gdef"}, "EnhancedEBMGenerator": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "ebm_integration_guide.EnhancedEBMGenerator", "name": "EnhancedEBMGenerator", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "ebm_integration_guide.EnhancedEBMGenerator", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "ebm_integration_guide", "mro": ["ebm_integration_guide.EnhancedEBMGenerator", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "llm_manager", "update_callback"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "ebm_integration_guide.EnhancedEBMGenerator.__init__", "name": "__init__", "type": null}}, "_assemble_full_report": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "sections", "statistics", "is_chinese"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "ebm_integration_guide.EnhancedEBMGenerator._assemble_full_report", "name": "_assemble_full_report", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "sections", "statistics", "is_chinese"], "arg_types": ["ebm_integration_guide.EnhancedEBMGenerator", {".class": "Instance", "args": ["builtins.str", "ebm_content_optimizer.EBMSection"], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_assemble_full_report of EnhancedEBMGenerator", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_generate_report_summary": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0], "arg_names": ["self", "sections", "statistics", "quality_metrics", "compliance_check", "is_chinese"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "ebm_integration_guide.EnhancedEBMGenerator._generate_report_summary", "name": "_generate_report_summary", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0], "arg_names": ["self", "sections", "statistics", "quality_metrics", "compliance_check", "is_chinese"], "arg_types": ["ebm_integration_guide.EnhancedEBMGenerator", {".class": "Instance", "args": ["builtins.str", "ebm_content_optimizer.EBMSection"], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "Instance", "args": ["builtins.str", "builtins.float"], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "Instance", "args": ["builtins.str", "builtins.bool"], "extra_attrs": null, "type_ref": "builtins.dict"}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_generate_report_summary of EnhancedEBMGenerator", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_update_progress": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "message", "is_error"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "ebm_integration_guide.EnhancedEBMGenerator._update_progress", "name": "_update_progress", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "message", "is_error"], "arg_types": ["ebm_integration_guide.EnhancedEBMGenerator", "builtins.str", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_update_progress of EnhancedEBMGenerator", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "content_optimizer": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "ebm_integration_guide.EnhancedEBMGenerator.content_optimizer", "name": "content_optimizer", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "generate_optimized_ebm_report": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 1], "arg_names": ["self", "topic", "studies", "provider", "model", "is_chinese"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "ebm_integration_guide.EnhancedEBMGenerator.generate_optimized_ebm_report", "name": "generate_optimized_ebm_report", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 1], "arg_names": ["self", "topic", "studies", "provider", "model", "is_chinese"], "arg_types": ["ebm_integration_guide.EnhancedEBMGenerator", "builtins.str", {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.list"}, "builtins.str", "builtins.str", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "generate_optimized_ebm_report of EnhancedEBMGenerator", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "llm": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "ebm_integration_guide.EnhancedEBMGenerator.llm", "name": "llm", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "update_callback": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "ebm_integration_guide.EnhancedEBMGenerator.update_callback", "name": "update_callback", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "ebm_integration_guide.EnhancedEBMGenerator.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "ebm_integration_guide.EnhancedEBMGenerator", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "List": {".class": "SymbolTableNode", "cross_ref": "typing.List", "kind": "Gdef"}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "ebm_integration_guide.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "ebm_integration_guide.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "ebm_integration_guide.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "ebm_integration_guide.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "ebm_integration_guide.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "ebm_integration_guide.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "example_usage": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "ebm_integration_guide.example_usage", "name": "example_usage", "type": null}}, "logger": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "ebm_integration_guide.logger", "name": "logger", "type": "logging.Logger"}}, "logging": {".class": "SymbolTableNode", "cross_ref": "logging", "kind": "Gdef"}}, "path": "E:\\xzyxgg\\ebm_integration_guide.py"}