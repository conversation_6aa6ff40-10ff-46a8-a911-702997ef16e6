xzyxgg/
│
├── extensions/              # 新增：扩展功能
│   ├── __init__.py
│   ├── visualization/       # 可视化扩展
│   │   ├── __init__.py
│   │   ├── forest_plot.py
│   │   ├── funnel_plot.py
│   │   └── rob_plot.py
│   │
│   └── reporting/          # 报告生成扩展
│       ├── __init__.py
│       ├── html_generator.py
│       └── pdf_exporter.py
│
├── static/                 # 静态资源
│   ├── css/
│   └── js/
│
├── templates/              # HTML模板
│   └── report.html
│
├── app.py                  # 主应用（不变）
├── config.py               # 配置（不变）
├── ebm_generator.py        # 核心文件（不变）
├── evidence_processor.py   # 核心文件（不变）
├── file_utils.py           # 核心文件（不变）
├── literature_search.py    # 核心文件（不变）
├── llm_manager.py          # 核心文件（不变）
├── prompts.py              # 核心文件（不变）
├── text_utils.py           # 核心文件（不变）
└── .env                    # 环境变量（不变）