# app.py (Final Secure Version with Lazy Initialization)
import gradio as gr
import os
import logging
import threading
import time
from typing import Dict, List, Optional
from functools import lru_cache # <--- Import lru_cache for caching
import json
# Import our custom modules
from config import settings
from llm_manager import LL<PERSON>anager
from literature_search import SearchService
from ebm_generator import EBMGenerator
from file_utils import save_markdown

# --- Logging Setup ---
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')

# --- LAZY INITIALIZATION & CACHING ---
# This ensures these heavy objects are created only once, when first needed.
@lru_cache(maxsize=None)
def get_llm_manager() -> LLMManager:
    logging.info("Initializing LLMManager for the first time...")
    return LLMManager()

@lru_cache(maxsize=None)
def get_search_service() -> SearchService:
    logging.info("Initializing SearchService for the first time...")
    # Pass the singleton instance of LLMManager
    return SearchService(get_llm_manager())

# --- Initial Data for UI (This part is safe and lightweight) ---
# We call the function here to get the data for the UI dropdowns.
# The @lru_cache ensures the underlying object is created only once.
available_models = get_llm_manager().get_available_models()
provider_choices = list(available_models.keys())

# --- UI Helper Functions ---
def update_model_choices(provider_name: str) -> gr.update:
    """Dynamically update model choices based on the selected provider."""
    # The models are already loaded, so this is fast.
    if provider_name in available_models:
        return gr.update(choices=available_models[provider_name], value=available_models[provider_name][0])
    return gr.update(choices=[], value=None)

def save_all_reports(topic: str, reports: Dict[str, str]) -> str:
    """Saves all generated reports to a timestamped folder.
    
    Args:
        topic: The topic of the reports
        reports: Dictionary of reports with format {report_name: content}
        
    Returns:
        str: Message indicating where reports were saved
    """
    from datetime import datetime
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    sanitized_topic = "".join(c for c in topic if c.isalnum() or c in " _-").rstrip()[:50]
    output_dir = os.path.join("output", f"{sanitized_topic}_{timestamp}")
    os.makedirs(output_dir, exist_ok=True)
    
    saved_files = []
    
    for key, content in reports.items():
        if not content or "Failed to generate" in content or "生成失败" in content:
            continue
            
        # 检查是否是HTML内容
        is_html = content.strip().lower().startswith('<!doctype html>') or '<html' in content.lower()
        
        # 设置文件扩展名
        file_ext = '.html' if is_html else '.md'
        file_path = os.path.join(output_dir, f"{key}{file_ext}")
        
        try:
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            saved_files.append(os.path.basename(file_path))
        except Exception as e:
            logging.error(f"Error saving report {key}: {str(e)}")
    
    # 生成保存的文件列表
    file_list = "\n".join([f"- {f}" for f in saved_files])
    return f"Reports saved in directory: {output_dir}\n\nSaved files:\n{file_list}"

# --- THE MAIN WORKFLOW FUNCTION ---
def run_generation_process(topic: str, provider: str, model: str, progress=gr.Progress()):
    log_messages = []
    def update_status_callback(message: str, is_error: bool = False):
        prefix = "❌ " if is_error else "➡️ "
        log_messages.append(f"{prefix} {message}")
        # 添加时间戳
        timestamp = time.strftime("%H:%M:%S")
        formatted_message = f"[{timestamp}] {prefix} {message}"
        log_messages.append(formatted_message)
        yield "\n".join(log_messages), None, None, None, None, None
    
    # --- Step 0: Get Service Instances ---
    # The first time this runs, it will create the objects.
    # Subsequent runs will get the cached objects instantly.
    llm_manager = get_llm_manager()
    search_service = get_search_service()
    
    yield from update_status_callback("🚀 Pipeline starting...")
    if not all([topic, provider, model]):
        yield from update_status_callback("Input Error: Topic, Provider, and Model must be selected.", is_error=True)
        return

    # --- Step 1: Literature Search ---
    progress(0.1, desc="Searching for literature...")
    yield from update_status_callback("🔍 Searching for relevant literature...")
    
    search_service.set_llm_config(provider, model)
    
    try:
        articles = search_service.search_all(topic)
        if not articles:
            yield from update_status_callback("❌ No relevant articles found. Please try a different search term.", is_error=True)
            return
            
        total_articles = len(articles)
        yield from update_status_callback(f"✅ Found {total_articles} relevant articles")
        
        # Save the raw search results for reference
        safe_topic = "".join(c for c in topic if c.isalnum() or c in " _-").rstrip()[:50]
        # Ensure output directory exists
        os.makedirs("output", exist_ok=True)
        search_results_file = os.path.join("output", f"{safe_topic}_search_results.json")
        with open(search_results_file, 'w', encoding='utf-8') as f:
            json.dump(articles, f, ensure_ascii=False, indent=2)
        yield from update_status_callback(f"📄 Saved search results to {search_results_file}")
        
        # Initialize evidence processor with enhanced error handling
        ebm_generator = EBMGenerator(llm_manager, update_status_callback)

        # Process studies in batches with progress updates
        progress(0.2, desc="Processing studies...")
        yield from update_status_callback("🔬 开始处理研究数据，提取证据...")

        process_result = ebm_generator.process_studies_incrementally(
            articles,
            provider=provider,
            model=model,
            batch_size=4  # Adjust batch size as needed
        )
        
        if process_result.get('status') != 'completed':
            error_msg = process_result.get('message', 'Unknown error during evidence processing')
            yield from update_status_callback(f"❌ Error processing evidence: {error_msg}", is_error=True)
            return
            
        # Get the processed evidence summary
        evidence_summary = process_result.get('summary', {})
        yield from update_status_callback(
            f"✅ Processed {process_result.get('processed', 0)} studies. "
            f"Identified {len(evidence_summary.get('themes', []))} themes"
        )

    except Exception as e:
        error_msg = f"Error processing literature search results: {str(e)}"
        logging.error(error_msg, exc_info=True)
        yield from update_status_callback(f"❌ {error_msg}", is_error=True)
        return

    # --- Step 2: Generate Reports ---
    try:
        all_reports_content = {}
        
        # Generate EBM Reports (Chinese and English)
        progress(0.4, desc="Generating EBM reports...")
        yield from update_status_callback("📝 生成EBM报告（中英文）...")
        yield from update_status_callback("📊 同时生成数据可视化图表...")

        # Initialize report variables
        ebm_report_en = None
        ebm_report_cn = None
        
        try:
            # Pass EBM data to the report generator
            ebm_report_en, ebm_report_cn = ebm_generator.generate_reports(
                topic=topic,
                articles_for_report=articles,  # Use all available articles for EBM report
                report_type="ebm",
                provider=provider,
                model=model
            )
        except Exception as e:
            error_msg = f"Error generating EBM reports: {str(e)}"
            logging.error(error_msg, exc_info=True)
            yield from update_status_callback(f"❌ {error_msg}", is_error=True)
            # Set reports to None to avoid further processing
            ebm_report_en = None
            ebm_report_cn = None
        
        # Handle Chinese EBM report (Markdown)
        if ebm_report_cn and os.path.exists(ebm_report_cn):
            try:
                with open(ebm_report_cn, 'r', encoding='utf-8') as f:
                    all_reports_content["ebm_report_cn"] = f.read()
                yield from update_status_callback("✅ 中文EBM报告(Markdown)生成成功")

                # Check for HTML version
                html_path_cn = ebm_report_cn.replace('.md', '.html')
                if os.path.exists(html_path_cn):
                    try:
                        with open(html_path_cn, 'r', encoding='utf-8') as f:
                            all_reports_content["ebm_report_cn_html"] = f.read()
                        yield from update_status_callback("✅ 中文EBM报告(HTML)生成成功")
                    except Exception as e:
                        yield from update_status_callback(f"⚠️ 读取中文HTML报告失败: {str(e)}", is_error=True)
                else:
                    yield from update_status_callback("⚠️ 中文HTML报告文件不存在", is_error=True)

            except Exception as e:
                error_msg = f"Error reading Chinese EBM report: {str(e)}"
                logging.error(error_msg, exc_info=True)
                yield from update_status_callback(f"❌ {error_msg}", is_error=True)
        else:
            yield from update_status_callback("⚠️ 中文EBM报告生成失败或文件不存在", is_error=True)
            
        # Handle English EBM report (Markdown)
        if ebm_report_en and os.path.exists(ebm_report_en):
            try:
                with open(ebm_report_en, 'r', encoding='utf-8') as f:
                    all_reports_content["ebm_report_en"] = f.read()
                yield from update_status_callback("✅ English EBM report (Markdown) generated successfully")

                # Check for HTML version
                html_path_en = ebm_report_en.replace('.md', '.html')
                if os.path.exists(html_path_en):
                    try:
                        with open(html_path_en, 'r', encoding='utf-8') as f:
                            all_reports_content["ebm_report_en_html"] = f.read()
                        yield from update_status_callback("✅ English EBM report (HTML) generated successfully")
                    except Exception as e:
                        yield from update_status_callback(f"⚠️ Failed to read English HTML report: {str(e)}", is_error=True)
                else:
                    yield from update_status_callback("⚠️ English HTML report file does not exist", is_error=True)

            except Exception as e:
                error_msg = f"Error reading English EBM report: {str(e)}"
                logging.error(error_msg, exc_info=True)
                yield from update_status_callback(f"❌ {error_msg}", is_error=True)
        else:
            yield from update_status_callback("⚠️ Failed to generate English EBM report or file does not exist", is_error=True)
        
        # Generate Narrative Reviews (Chinese and English)
        progress(0.6, desc="Generating narrative reviews...")
        yield from update_status_callback("📝 Generating narrative reviews in Chinese and English...")
        
        # Initialize narrative review variables
        narrative_review_en = None
        narrative_review_cn = None
        
        try:
            narrative_review_en, narrative_review_cn = ebm_generator.generate_reports(
                topic=topic,
                articles_for_report=articles[:20],  # Limit to first 20 articles for narrative review
                report_type="narrative",
                provider=provider,
                model=model
            )
        except Exception as e:
            error_msg = f"Error generating narrative reviews: {str(e)}"
            logging.error(error_msg, exc_info=True)
            yield from update_status_callback(f"❌ {error_msg}", is_error=True)
            # Set reports to None to avoid further processing
            narrative_review_en = None
            narrative_review_cn = None
            
        # Handle Chinese narrative review
        if narrative_review_cn and os.path.exists(narrative_review_cn):
            try:
                with open(narrative_review_cn, 'r', encoding='utf-8') as f:
                    all_reports_content["narrative_review_cn"] = f.read()
                yield from update_status_callback("✅ 中文文献综述生成成功")
            except Exception as e:
                error_msg = f"Error reading Chinese narrative review: {str(e)}"
                logging.error(error_msg, exc_info=True)
                yield from update_status_callback(f"❌ {error_msg}", is_error=True)
        else:
            yield from update_status_callback("⚠️ 中文文献综述生成失败或文件不存在", is_error=True)
            
        # Handle English narrative review
        if narrative_review_en and os.path.exists(narrative_review_en):
            try:
                with open(narrative_review_en, 'r', encoding='utf-8') as f:
                    all_reports_content["narrative_review_en"] = f.read()
                yield from update_status_callback("✅ English narrative review generated successfully")
            except Exception as e:
                error_msg = f"Error reading English narrative review: {str(e)}"
                logging.error(error_msg, exc_info=True)
                yield from update_status_callback(f"❌ {error_msg}", is_error=True)
        else:
            yield from update_status_callback("⚠️ Failed to generate English narrative review or file does not exist", is_error=True)

        # --- Step 3: Finalization ---
        progress(0.9, desc="Finalizing and saving...")
        yield from update_status_callback("Saving all reports...")
        
        # Save all collected reports
        if all_reports_content:
            save_message = save_all_reports(topic, all_reports_content)
            yield from update_status_callback(f"✅ All reports saved successfully: {save_message}")
        else:
            save_message = "No reports were generated"
            yield from update_status_callback("⚠️ No reports were generated to save", is_error=True)
        
        # Return the content of the reports for display
        yield (
            "\n".join(log_messages),
            all_reports_content.get("ebm_report_en", "Failed to generate English EBM report"),
            all_reports_content.get("ebm_report_cn", "生成中文EBM报告失败"),
            all_reports_content.get("narrative_review_en", "Failed to generate English narrative review"),
            all_reports_content.get("narrative_review_cn", "生成中文叙述性综述失败"),
            save_message
        )
        
        yield from update_status_callback(f"✅ 处理完成! {save_message}")
        
    except Exception as e:
        error_msg = f"生成报告时发生错误: {str(e)}"
        logging.error(error_msg, exc_info=True)
        yield from update_status_callback(f"❌ 错误: {error_msg}")
        
        # Return error messages for display
        yield (
            "\n".join(log_messages + [f"Error: {error_msg}"]),
            "Error: Failed to generate English EBM report",
            "错误: 生成中文EBM报告失败",
            "Error: Failed to generate English narrative review",
            "错误: 生成中文叙述性综述失败",
            "Error occurred while generating reports. Please check the logs for details."
        )

# --- Gradio UI Definition ---
with gr.Blocks(theme=gr.themes.Soft(), title="LIAI EBM & Review Tool") as demo:
    gr.Markdown("# 🤖 LIAI循证医学(EBM)与文献综述TOOL (v3.3 - Startup Fixed)")
    gr.Markdown("专为LIKEVIN设计，为人类健康服务 (安全修复与优化版)")
    
    with gr.Row():
        topic_input = gr.Textbox(label="研究主题 (Topic)", placeholder="例如: Aspirin for primary prevention of cardiovascular disease", scale=3)
        provider_dropdown = gr.Dropdown(label="选择LLM提供商", choices=provider_choices, value=provider_choices[0] if provider_choices else None, scale=1)
        model_dropdown = gr.Dropdown(label="选择模型", choices=available_models.get(provider_choices[0], []) if provider_choices else [], scale=2)
        if provider_choices and available_models.get(provider_choices[0]):
            model_dropdown.value = available_models.get(provider_choices[0])[0]

    submit_button = gr.Button("🚀 生成报告 (Assemble Reports)", variant="primary")

    with gr.Accordion("任务日志与进度 (Live Process Log)", open=True):
        log_output = gr.Textbox(label="生成日志 (Log)", lines=10, interactive=False)
        final_status_output = gr.Textbox(label="最终状态 (Final Status)", interactive=False)

    with gr.Tabs():
        with gr.TabItem("EBM Report (English)"):
            ebm_en_output = gr.Markdown()
        with gr.TabItem("循证医学报告 (中文)"):
            ebm_cn_output = gr.Markdown()
        with gr.TabItem("Narrative Review (English)"):
            narrative_en_output = gr.Markdown()
        with gr.TabItem("文献综述 (中文)"):
            narrative_cn_output = gr.Markdown()
    
    provider_dropdown.change(fn=update_model_choices, inputs=provider_dropdown, outputs=model_dropdown)
    
    submit_button.click(
        fn=run_generation_process,
        inputs=[topic_input, provider_dropdown, model_dropdown],
        outputs=[log_output, ebm_en_output, ebm_cn_output, narrative_en_output, narrative_cn_output, final_status_output]
    )

def find_available_port(start_port=7861, max_attempts=10):
    """Try to find an available port in the specified range."""
    import socket
    
    for port in range(start_port, start_port + max_attempts):
        try:
            # Try to bind to the port
            with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
                s.bind(('127.0.0.1', port))
                return port  # Port is available
        except OSError:
            continue  # Port is in use, try next one
    return 0  # No available port found, let OS choose

if __name__ == "__main__":
    # Try to find an available port in the range 7861-7870
    port = find_available_port(7861, 10)
    
    if port == 0:
        print("No available port found in range 7861-7870, letting OS choose a port...")
        port = 0
    else:
        print(f"Found available port: {port}")
    
    # Launch with the found port
    demo.queue().launch(
        server_name="127.0.0.1",
        server_port=port,
        share=False,
        inbrowser=True,
        show_error=True
    )