import os
import sys
import datetime
from pathlib import Path

def ensure_output_dir(base_dir: str = "output") -> Path:
    """Ensure the output directory exists with proper encoding handling."""
    try:
        if sys.platform == 'win32':
            # On Windows, use a basic ASCII path to avoid encoding issues
            base_dir = "output"
        
        output_dir = Path(base_dir)
        output_dir.mkdir(parents=True, exist_ok=True)
        return output_dir
    except Exception as e:
        # Fallback to a basic ASCII path if there's any error
        output_dir = Path("output")
        output_dir.mkdir(parents=True, exist_ok=True)
        return output_dir

def sanitize_filename(filename: str) -> str:
    """Sanitize filename to be filesystem-safe."""
    # Keep only alphanumeric, spaces, and a few safe characters
    safe_chars = " -_()"
    return ''.join(c if c.isalnum() or c in safe_chars else '_' for c in filename)

def save_markdown(content: str, keyword: str = "report", output_dir: str = None) -> str:
    """
    Save content to a markdown file with timestamp and keyword in the filename.
    
    Args:
        content: The markdown content to save
        keyword: A keyword to include in the filename
        output_dir: Optional directory to save the file in
        
    Returns:
        str: The path to the saved file
    """
    try:
        # Create a safe filename
        timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
        safe_keyword = sanitize_filename(keyword)[:50]  # Sanitize keyword
        filename = f"{timestamp}_{safe_keyword}.md"
        
        # Ensure the output directory exists
        if output_dir:
            output_path = ensure_output_dir(output_dir)
        else:
            output_path = ensure_output_dir()
        
        # Create the full file path
        filepath = output_path / filename
        
        # Write the content with UTF-8 encoding
        with open(str(filepath), 'w', encoding='utf-8') as f:
            f.write(content)
            
        return str(filepath)
    except Exception as e:
        # Fallback to a simple filename in the current directory
        try:
            fallback_filename = f"output_{int(datetime.datetime.now().timestamp())}.md"
            with open(fallback_filename, 'w', encoding='utf-8') as f:
                f.write(content)
            return fallback_filename
        except:
            # Last resort - print to console
            print("Error saving file, content:", content[:500] + "..." if len(content) > 500 else content)
            return ""

    # Save the content
    with open(filepath, 'w', encoding='utf-8') as f:
        f.write(content)
    
    return str(filepath)
