"""
Quality analysis module for EBM (Evidence-Based Medicine) reports.
Handles quality assessment of studies and caches results for reuse.
"""

import os
import json
import logging
from dataclasses import dataclass, asdict, field
from datetime import datetime
from typing import Dict, List, Optional, Any
from pathlib import Path

logger = logging.getLogger(__name__)

@dataclass
class StudyQualityResult:
    """Data class to store quality assessment results for a single study."""
    study_id: str
    quality_score: float  # 0-10 scale
    risk_of_bias: str  # Low, Moderate, High
    limitations: List[str]
    strengths: List[str]
    assessment_date: str = field(default_factory=lambda: datetime.now().isoformat())
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for serialization."""
        return asdict(self)
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'StudyQualityResult':
        """Create from dictionary."""
        return cls(**data)

class QualityAnalyzer:
    """
    Handles quality analysis of studies and caches results for reuse.
    
    This ensures that quality analysis is only performed once per study,
    and the results are cached for future use.
    """
    
    def __init__(self, cache_dir: str = "cache/quality"):
        """
        Initialize the QualityAnalyzer with a cache directory.
        
        Args:
            cache_dir: Directory to store cached quality analysis results
        """
        self.cache_dir = Path(cache_dir)
        self.cache_dir.mkdir(parents=True, exist_ok=True)
        self._cache: Dict[str, StudyQualityResult] = {}
        self._load_cache()
    
    def _get_cache_file(self, study_id: str) -> Path:
        """Get the cache file path for a study."""
        # Use first 8 chars of study_id as filename to avoid too long filenames
        filename = f"{study_id[:8]}.json"
        return self.cache_dir / filename
    
    def _load_cache(self) -> None:
        """Load cached quality analysis results from disk."""
        if not self.cache_dir.exists():
            return
            
        for cache_file in self.cache_dir.glob("*.json"):
            try:
                with open(cache_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    result = StudyQualityResult.from_dict(data)
                    self._cache[result.study_id] = result
            except (json.JSONDecodeError, KeyError, TypeError) as e:
                logger.warning(f"Failed to load cache file {cache_file}: {e}")
    
    def analyze_study_quality(self, study: Dict[str, Any], provider: str, model: str) -> StudyQualityResult:
        """
        Analyze the quality of a single study, using cached results if available.
        
        Args:
            study: Study data dictionary
            provider: LLM provider name
            model: Model name
            
        Returns:
            StudyQualityResult: Quality assessment result
        """
        study_id = study.get('id') or study.get('title', 'unknown')
        
        # Check cache first
        if study_id in self._cache:
            return self._cache[study_id]
        
        # If not in cache, perform analysis
        logger.info(f"Performing quality analysis for study: {study_id}")
        
        # TODO: Implement actual quality analysis using LLM
        # This is a placeholder implementation
        quality_score = 7.5  # Placeholder value
        risk_of_bias = "Moderate"  # Placeholder value
        limitations = ["Sample size not reported", "Allocation concealment unclear"]
        strengths = ["Randomized controlled trial", "Double-blinded"]
        
        result = StudyQualityResult(
            study_id=study_id,
            quality_score=quality_score,
            risk_of_bias=risk_of_bias,
            limitations=limitations,
            strengths=strengths
        )
        
        # Cache the result
        self._cache[study_id] = result
        self._save_to_cache(result)
        
        return result
    
    def analyze_studies_quality(self, studies: List[Dict[str, Any]], provider: str, model: str) -> Dict[str, StudyQualityResult]:
        """
        Analyze the quality of multiple studies.
        
        Args:
            studies: List of study data dictionaries
            provider: LLM provider name
            model: Model name
            
        Returns:
            Dict mapping study IDs to StudyQualityResult objects
        """
        results = {}
        for study in studies:
            study_id = study.get('id') or study.get('title', 'unknown')
            results[study_id] = self.analyze_study_quality(study, provider, model)
        return results
    
    def _save_to_cache(self, result: StudyQualityResult) -> None:
        """Save a quality analysis result to the cache."""
        try:
            cache_file = self._get_cache_file(result.study_id)
            with open(cache_file, 'w', encoding='utf-8') as f:
                json.dump(result.to_dict(), f, ensure_ascii=False, indent=2)
        except (IOError, TypeError) as e:
            logger.error(f"Failed to save quality analysis to cache: {e}")
    
    def get_cached_result(self, study_id: str) -> Optional[StudyQualityResult]:
        """
        Get a cached quality analysis result.
        
        Args:
            study_id: ID of the study
            
        Returns:
            Cached StudyQualityResult, or None if not found
        """
        return self._cache.get(study_id)
    
    def clear_cache(self) -> None:
        """Clear all cached quality analysis results."""
        self._cache = {}
        for cache_file in self.cache_dir.glob("*.json"):
            try:
                cache_file.unlink()
            except OSError as e:
                logger.error(f"Failed to delete cache file {cache_file}: {e}")

# For backward compatibility
QualityAnalyzer = QualityAnalyzer