{"data_mtime": 1751436777, "dep_lines": [4, 5, 1, 2, 4, 1, 1, 1], "dep_prios": [10, 5, 10, 5, 20, 5, 30, 30], "dependencies": ["multiprocessing.popen_fork", "multiprocessing.util", "sys", "typing", "multiprocessing", "builtins", "_frozen_importlib", "abc"], "hash": "e7e6806fb862820fd183b28d1bdea23501161aef", "id": "multiprocessing.popen_spawn_posix", "ignore_all": true, "interface_hash": "7924fbd40f9b33e08ab7c627e76ce91033db9a0c", "mtime": 1741238380, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "c:\\Users\\<USER>\\.vscode\\extensions\\ms-python.mypy-type-checker-2025.2.0\\bundled\\libs\\mypy\\typeshed\\stdlib\\multiprocessing\\popen_spawn_posix.pyi", "plugin_data": null, "size": 524, "suppressed": [], "version_id": "1.15.0"}