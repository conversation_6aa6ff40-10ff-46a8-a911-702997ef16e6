{"data_mtime": 1751359262, "dep_lines": [3, 1, 2, 4, 5, 6, 1, 1, 1, 1], "dep_prios": [5, 10, 5, 5, 5, 5, 5, 30, 30, 30], "dependencies": ["collections.abc", "sys", "_typeshed", "os", "typing", "typing_extensions", "builtins", "_frozen_importlib", "abc", "types"], "hash": "776d16d8b3327bdf2554440e5860ecc3d293b163", "id": "importlib.metadata._meta", "ignore_all": true, "interface_hash": "0d207caec1811eee54cec87d242084452cad1626", "mtime": 1741238379, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "c:\\Users\\<USER>\\.vscode\\extensions\\ms-python.mypy-type-checker-2025.2.0\\bundled\\libs\\mypy\\typeshed\\stdlib\\importlib\\metadata\\_meta.pyi", "plugin_data": null, "size": 2552, "suppressed": [], "version_id": "1.15.0"}