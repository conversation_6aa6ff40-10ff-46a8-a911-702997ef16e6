{"data_mtime": 1751436800, "dep_lines": [2, 1, 1, 1, 1, 1], "dep_prios": [5, 10, 5, 30, 30, 30], "dependencies": ["collections.abc", "sys", "builtins", "_frozen_importlib", "abc", "typing"], "hash": "2f7a3af9e2f5f9cc961f7d9e964369710db9a8ba", "id": "errno", "ignore_all": true, "interface_hash": "8ae3b104c425d340b375e58dcddb63e3ed479ebb", "mtime": 1741238379, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "c:\\Users\\<USER>\\.vscode\\extensions\\ms-python.mypy-type-checker-2025.2.0\\bundled\\libs\\mypy\\typeshed\\stdlib\\errno.pyi", "plugin_data": null, "size": 3957, "suppressed": [], "version_id": "1.15.0"}