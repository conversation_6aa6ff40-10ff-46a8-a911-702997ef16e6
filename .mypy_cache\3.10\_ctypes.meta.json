{"data_mtime": 1751436776, "dep_lines": [5, 1, 2, 4, 6, 7, 8, 11, 1, 1], "dep_prios": [5, 5, 10, 5, 5, 5, 5, 5, 5, 30], "dependencies": ["collections.abc", "_typeshed", "sys", "abc", "ctypes", "typing", "typing_extensions", "types", "builtins", "_frozen_importlib"], "hash": "734ab50eb9ca6dabb0c54e43b582a4438d0534e8", "id": "_ctypes", "ignore_all": true, "interface_hash": "1a4d9440a037313f1b3851c74a8d2c3f1b4fff16", "mtime": 1741238378, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "c:\\Users\\<USER>\\.vscode\\extensions\\ms-python.mypy-type-checker-2025.2.0\\bundled\\libs\\mypy\\typeshed\\stdlib\\_ctypes.pyi", "plugin_data": null, "size": 16448, "suppressed": [], "version_id": "1.15.0"}