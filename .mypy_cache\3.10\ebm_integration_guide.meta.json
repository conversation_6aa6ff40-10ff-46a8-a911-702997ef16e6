{"data_mtime": 1751448026, "dep_lines": [8, 9, 10, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 5, 5, 5, 30, 30, 30, 30, 30], "dependencies": ["logging", "typing", "ebm_content_optimizer", "builtins", "_frozen_importlib", "_typeshed", "abc", "types", "typing_extensions"], "hash": "7dc213823a4bc2e9771a768c4eb71a50c4d00629", "id": "ebm_integration_guide", "ignore_all": false, "interface_hash": "cfcf68101aadd7e54e247b766b9a6a85f3a1d91b", "mtime": 1751335704, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "E:\\xzyxgg\\ebm_integration_guide.py", "plugin_data": null, "size": 14195, "suppressed": [], "version_id": "1.15.0"}