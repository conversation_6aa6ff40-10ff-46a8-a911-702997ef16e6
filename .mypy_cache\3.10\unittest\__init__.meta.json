{"data_mtime": 1751436824, "dep_lines": [2, 4, 14, 15, 16, 17, 18, 24, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 5, 10, 5, 30, 30, 30, 30], "dependencies": ["unittest.async_case", "unittest.case", "unittest.loader", "unittest.main", "unittest.result", "unittest.runner", "unittest.signals", "unittest.suite", "sys", "builtins", "_frozen_importlib", "_typeshed", "abc", "typing"], "hash": "df7e6bc2d1dc8e8d1bbf01a394ef31e56e0665dc", "id": "unittest", "ignore_all": true, "interface_hash": "74adf6ba82a88746aa33203f659ef5966792917a", "mtime": 1741238380, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "c:\\Users\\<USER>\\.vscode\\extensions\\ms-python.mypy-type-checker-2025.2.0\\bundled\\libs\\mypy\\typeshed\\stdlib\\unittest\\__init__.pyi", "plugin_data": null, "size": 1848, "suppressed": [], "version_id": "1.15.0"}