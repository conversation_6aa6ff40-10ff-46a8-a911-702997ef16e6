import requests
import json
import logging
from tenacity import retry, stop_after_attempt, wait_exponential
from config import settings
from typing import Dict, List, Optional, Any

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('app.log', encoding='utf-8')
    ]
)
logger = logging.getLogger(__name__)

# 自定义异常类
class ContentFilteredException(Exception):
    """当内容被模型提供商过滤时抛出的异常"""
    pass

class LLMManager:
    """
    一个统一的、支持多提供商的LLM管理器。
    """
    def __init__(self):
        self.providers = {}
        
        # Define available models for each provider
        self.available_models = {
            'ZhipuAI': [
                'glm-4-flash',
                'glm-z1-flash',
                'glm-4-flash-250414'
            ],
            'OpenRouter': [
                'deepseek/deepseek-v3-base:free',
                'google/gemini-2.5-pro-exp-03-25:free',
                'deepseek/deepseek-r1-zero:free',
                'nvidia/llama-3.1-nemotron-ultra-253b-v1:free',
                'meta-llama/llama-3.3-70b-instruct:free',
                'meta-llama/llama-4-scout:free',
                'moonshotai/kimi-dev-72b:free',
                'featherless/qwerky-72b:free',
                'qwen/qwen-2.5-72b-instruct:free',
                'qwen/qwq-32b-preview:free',
                'deepseek/deepseek-chat:free',
                'deepseek/deepseek-chat-v3-0324:free',
                'microsoft/mai-ds-r1:free',
                'microsoft/phi-4-reasoning-plus:free',
                'deepseek/deepseek-prover-v2:free',
                'qwen/qwen3-235b-a22b:free',
                'tngtech/deepseek-r1t-chimera:free',
                'thudm/glm-4-32b:free',
                'qwen/qwq-32b:free'
            ],
            'SiliconFlow': [
                'THUDM/glm-4-9b-chat',
                'deepseek-ai/DeepSeek-R1-Distill-Qwen-7B',
                'Qwen/Qwen2.5-7B-Instruct'
            ],
            'DeepSeek': [
                'deepseek-chat',
                'deepseek-coder'
            ]
        }
        
        self._initialize_providers()

    def _initialize_providers(self):
        # 1. ZhipuAI (GLM)
        if settings.ZHIPUAI_API_KEY:
            self.providers['ZhipuAI'] = {
                "api_key": settings.ZHIPUAI_API_KEY,
                "base_url": "https://open.bigmodel.cn/api/paas/v4/",
                "models": self.available_models['ZhipuAI']
            }

        # 2. OpenRouter
        if settings.OPENROUTER_API_KEY:
            self.providers['OpenRouter'] = {
                "api_key": settings.OPENROUTER_API_KEY,
                "base_url": "https://openrouter.ai/api/v1",
                "models": self.available_models['OpenRouter']
            }

        # 3. SiliconFlow
        if settings.SILICONFLOW_API_KEY:
            self.providers['SiliconFlow'] = {
                "api_key": settings.SILICONFLOW_API_KEY,
                "base_url": "https://api.siliconflow.cn/v1/chat/completions",
                "models": self.available_models['SiliconFlow']
            }

    def get_available_models(self):
        """返回所有可用模型的字典，供Gradio的Dropdown使用"""
        model_dict = {}
        for provider_name, config in self.providers.items():
            model_dict[provider_name] = config['models']
        return model_dict

    @retry(stop=stop_after_attempt(3), wait=wait_exponential(multiplier=1, min=2, max=10))
    def generate(self, provider_name: str, model_name: str, system_prompt: str, user_prompt: str, temperature: float = 0.5, max_tokens: int = 2000):
        logger = logging.getLogger(__name__)
        
        if provider_name not in self.providers:
            error_msg = f"Provider '{provider_name}' is not configured or available."
            logger.error(error_msg)
            raise ValueError(error_msg)

        logger.info(f"Using model: {provider_name}/{model_name}")
        
        try:
            if provider_name == 'SiliconFlow':
                return self._generate_siliconflow(model_name, system_prompt, user_prompt, temperature)
            else:
                # ZhipuAI 和 OpenRouter 共享 OpenAI-compatible 接口
                return self._generate_openai_compatible(
                    provider_name=provider_name,
                    model_name=model_name,
                    system_prompt=system_prompt,
                    user_prompt=user_prompt,
                    temperature=temperature,
                    max_tokens=max_tokens
                )
        except Exception as e:
            logger.error(f"Error generating with {provider_name}/{model_name}: {str(e)}", exc_info=True)
            raise

    def _generate_openai_compatible(self, provider_name: str, model_name: str, system_prompt: str, user_prompt: str, temperature: float, max_tokens: int = 2000):
        config = self.providers[provider_name]
        logger = logging.getLogger(__name__)
        
        # Common headers
        headers = {
            "Content-Type": "application/json"
        }
        
        # Provider-specific configurations
        if provider_name == 'OpenRouter':
            headers["Authorization"] = f"Bearer {config['api_key']}"
            headers["HTTP-Referer"] = settings.YOUR_SITE_URL
            headers["X-Title"] = settings.YOUR_SITE_NAME
            endpoint = f"{config['base_url']}/chat/completions"
        elif provider_name == 'ZhipuAI':
            # ZhipuAI requires the API key in the Authorization header with 'Bearer ' prefix
            headers["Authorization"] = f"Bearer {config['api_key']}"
            endpoint = f"{config['base_url']}chat/completions"
        else:
            headers["Authorization"] = f"Bearer {config['api_key']}"
            endpoint = config['base_url']
        
        # Construct payload based on provider
        if provider_name == 'ZhipuAI':
            # For ZhipuAI, combine system and user prompts into a single user message
            # as it's more reliable with their API
            combined_prompt = f"{system_prompt}\n\n{user_prompt}" if system_prompt.strip() else user_prompt
            
            payload = {
                "model": model_name,
                "messages": [
                    {"role": "user", "content": combined_prompt}
                ],
                "temperature": max(0.1, min(temperature, 1.0)),  # Ensure temperature is within valid range
                "top_p": 0.7,
                "max_tokens": max_tokens
            }
            
            # Add stream parameter if needed
            payload["stream"] = False
        else:
            payload = {
                "model": model_name,
                "messages": [
                    {"role": "system", "content": system_prompt},
                    {"role": "user", "content": user_prompt}
                ],
                "temperature": temperature,
                "top_p": 1.0,
                "max_tokens": max_tokens
            }
        
        logger.debug(f"Sending request to {endpoint} with payload: {json.dumps(payload, ensure_ascii=False, indent=2)}")
        
        try:
            response = requests.post(
                endpoint,
                headers=headers,
                json=payload,
                timeout=60  # 60 seconds timeout
            )
            response.raise_for_status()
            
            data = response.json()
            
            # Handle different response formats
            if provider_name == 'ZhipuAI':
                # Log the full response for debugging
                logger.debug(f"ZhipuAI response: {json.dumps(data, ensure_ascii=False, indent=2)}")
                
                # Try different response formats
                if 'choices' in data and isinstance(data['choices'], list) and len(data['choices']) > 0:
                    choice = data['choices'][0]
                    if isinstance(choice, dict) and 'message' in choice and 'content' in choice['message']:
                        return choice['message']['content']
                
                if 'output' in data and isinstance(data['output'], str):
                    return data['output']
                    
                # If we get here, the response format is unexpected
                error_msg = f"Unexpected response format from {provider_name}: {data}"
                logger.error(error_msg)
                raise ValueError(error_msg)
            else:
                # Standard OpenAI-compatible response
                if 'choices' in data and isinstance(data['choices'], list) and len(data['choices']) > 0:
                    return data['choices'][0]['message']['content']
                raise ValueError(f"Unexpected response format: {data}")
                
        except requests.exceptions.RequestException as e:
            error_msg = f"Request to {provider_name} failed: {str(e)}"
            if hasattr(e, 'response') and e.response is not None:
                error_msg += f"\nResponse status: {e.response.status_code}\nResponse body: {e.response.text}"

                # 检查是否是内容过滤错误
                if e.response.status_code == 400 and "contentFilter" in e.response.text:
                    logger.warning(f"Content filtered by {provider_name}. This content may be sensitive.")
                    # 抛出特定的内容过滤异常
                    raise ContentFilteredException(f"Content filtered by {provider_name}")

            logger.error(error_msg)
            raise

    def _generate_siliconflow(self, model_name: str, system_prompt: str, user_prompt: str, temperature: float):
        config = self.providers['SiliconFlow']
        headers = {
            "Authorization": f"Bearer {config['api_key']}",
            "Content-Type": "application/json"
        }
        
        # SiliconFlow 将 system prompt 合并到 messages
        payload = {
            "model": model_name,
            "messages": [
                {"role": "system", "content": system_prompt},
                {"role": "user", "content": user_prompt}
            ],
            "temperature": temperature,
            "top_p": 0.7,
            "stream": False
        }

        response = requests.post(config['base_url'], headers=headers, json=payload)
        response.raise_for_status()
        
        data = response.json()
        return data['choices'][0]['message']['content']