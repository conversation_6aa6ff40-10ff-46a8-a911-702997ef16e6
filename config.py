import os
from dotenv import load_dotenv

load_dotenv()

class Settings:
    # --- LLM Provider Keys ---
    ZHIPUAI_API_KEY = os.getenv("ZHIPUAI_API_KEY")
    OPENROUTER_API_KEY = os.getenv("OPENROUTER_API_KEY")
    SILICONFLOW_API_KEY = os.getenv("SILICONFLOW_API_KEY")
    
    # --- OpenRouter Specific ---
    YOUR_SITE_URL = os.getenv("YOUR_SITE_URL", "http://localhost:7860")
    YOUR_SITE_NAME = os.getenv("YOUR_SITE_NAME", "EBM & Review AI")
    
    # --- PubMed Config ---
    PUBMED_EMAIL = os.getenv("PUBMED_EMAIL")
    MAX_RESULTS_PER_SOURCE = 100 # 增加到100，支持更多研究数据
    SEARCH_YEARS = 5

    # --- App Config ---
    # 为小模型优化的批处理大小
    ARTICLE_BATCH_SIZE = 2

settings = Settings()