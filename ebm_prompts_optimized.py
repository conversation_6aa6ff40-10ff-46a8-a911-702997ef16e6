# ebm_prompts_optimized.py
"""
针对小模型2k上下文限制优化的EBM提示词库
专门设计用于分块处理和真实数据驱动的内容生成
确保每个提示词都在上下文限制内，同时保持专业性
"""

# 优化的中文提示词 - 针对小模型上下文限制
OPTIMIZED_PROMPTS_CN = {
    
    # 标题生成 - 简化版本，专注核心要素
    'generate_professional_title': """
你是循证医学专家。基于真实研究数据生成专业标题：

主题：{topic}
研究：{study_count}项
设计：{main_designs}
参与者：{participants}人

要求：
1. 包含PICO要素
2. 标注"系统评价与Meta分析"
3. 30字内
4. 医学术语规范

格式：[干预]对比[对照]治疗[疾病][结局]的系统评价与Meta分析

生成标题：
""",

    # 摘要背景 - 压缩版本
    'abstract_background': """
为系统评价撰写摘要背景（80-120字）：

主题：{topic}
研究：{study_count}项
设计：{designs}

要求：
1. 临床重要性
2. 现有治疗局限性
3. 系统评价必要性
4. 基于真实证据

撰写背景：
""",

    # 摘要目的 - 精简版本
    'abstract_objective': """
撰写系统评价目的（50-80字）：

主题：{topic}
干预：{interventions}
人群：{population}

要求：
1. PICO框架
2. 主要结局
3. 语言准确

撰写目的：
""",

    # 摘要方法 - 核心要素
    'abstract_methods': """
撰写系统评价方法（100-150字）：

研究：{study_count}项
设计：{designs}
质量：{quality_info}

要求：
1. 检索策略和数据库
2. 纳入排除标准
3. 质量评估方法（ROB、GRADE）
4. 数据提取分析

撰写方法：
""",

    # 摘要结果 - 数据驱动
    'abstract_results': """
撰写系统评价结果（120-180字）：

研究：{study_count}项
参与者：{participants}人
设计分布：{design_dist}
样本范围：{sample_range}

要求：
1. 研究数量和参与者
2. 主要结局合并结果
3. 异质性分析
4. 具体统计数据

撰写结果：
""",

    # 摘要结论 - 证据基础
    'abstract_conclusion': """
撰写系统评价结论（80-120字）：

主题：{topic}
证据质量：{evidence_quality}
研究：{study_count}项

要求：
1. 回答研究问题
2. 证据质量等级
3. 临床建议
4. 未来研究方向

撰写结论：
""",

    # 引言部分 - 分块处理
    'introduction_background': """
撰写引言背景部分（200-300字）：

主题：{topic}
研究数据：{study_count}项研究，{participants}人
发表年份：{year_range}

要求：
1. {topic}的临床意义和流行病学负担
2. 当前标准治疗和局限性
3. 基于真实医学证据
4. 专业术语准确

撰写背景：
""",

    # 引言理论依据
    'introduction_rationale': """
撰写引言理论依据（100-150字）：

主题：{topic}
现有研究：{study_count}项
质量分布：{quality_dist}

要求：
1. 知识空白具体说明
2. 现有证据不足原因
3. 本综述推进作用
4. 逻辑清晰

撰写理论依据：
""",

    # 引言目标
    'introduction_objectives': """
撰写引言目标（100-150字）：

主题：{topic}
人群：{populations}
干预：{interventions}

要求：
1. PICO框架主要问题
2. 次要目标
3. 可测量结局指标
4. 明确具体

撰写目标：
""",

    # 方法-研究方案
    'methods_protocol': """
撰写方法-研究方案部分（150-200字）：

研究类型：系统评价与Meta分析
注册情况：{registration_info}

要求：
1. 方案预先制定情况
2. 公共平台注册（PROSPERO等）
3. 遵循PRISMA 2020指南
4. 透明度和可重复性

撰写研究方案：
""",

    # 方法-纳入排除标准
    'methods_eligibility': """
撰写纳入排除标准（200-250字）：

真实数据基础：
- 研究：{study_count}项
- 设计：{designs}
- 人群：{populations}
- 干预：{interventions}

要求：
1. PICOS框架详细条件
2. 人群、干预、对照、结局、设计要求
3. 基于实际纳入研究特征
4. 标准明确可操作

撰写标准：
""",

    # 方法-检索策略
    'methods_search': """
撰写检索策略（150-200字）：

检索结果：{study_count}项研究
时间范围：{year_range}
语言：中英文

要求：
1. 电子数据库（PubMed, EMBASE, Cochrane, 知网）
2. 检索时间和语言限制
3. 其他来源（灰色文献、会议摘要）
4. 检索策略透明

撰写检索策略：
""",

    # 方法-质量评价
    'methods_quality': """
撰写质量评价方法（150-200字）：

研究质量分布：{quality_dist}
评价工具：ROB工具、GRADE系统

要求：
1. 评价工具选择理由
2. 评价过程（独立双人）
3. 分歧解决机制
4. GRADE证据分级

撰写质量评价：
""",

    # 结果-研究特征
    'results_characteristics': """
撰写研究特征（200-300字）：

纳入研究：{study_count}项
参与者：{participants}人
设计分布：{design_dist}
年份范围：{year_range}
样本量：{sample_range}

要求：
1. 研究基本特征表格描述
2. 参与者人口统计学
3. 干预对照详情
4. 结局指标和随访
5. 客观描述真实数据

撰写特征：
""",

    # 结果-质量评估
    'results_quality': """
撰写质量评估结果（150-200字）：

质量分布：{quality_dist}
偏倚风险：基于ROB评估
GRADE评级：{grade_summary}

要求：
1. 各研究偏倚风险
2. 总体质量评价
3. GRADE证据确定性
4. 图表结合展示

撰写质量评估：
""",

    # 结果-主要发现
    'results_main_findings': """
撰写主要结果（200-300字）：

研究数据：{study_count}项，{participants}人
主要结局：基于真实合并分析
异质性：{heterogeneity_info}

要求：
1. 主要结局合并结果
2. 效应量和置信区间
3. 异质性检验
4. 森林图描述
5. 具体统计数据

撰写主要结果：
""",

    # 讨论-主要发现
    'discussion_findings': """
撰写讨论-主要发现（200-300字）：

主题：{topic}
证据：{study_count}项研究，{participants}人
质量：{evidence_quality}

要求：
1. 关键发现总结
2. 证据强度和方向
3. 回答研究问题
4. 基于真实数据分析

撰写主要发现：
""",

    # 讨论-局限性
    'discussion_limitations': """
撰写讨论-局限性（150-200字）：

研究质量：{quality_dist}
异质性：{heterogeneity_info}
偏倚风险：{bias_summary}

要求：
1. 纳入研究局限性
2. 方法学局限性
3. 异质性影响
4. 发表偏倚可能
5. 客观坦诚

撰写局限性：
""",

    # 讨论-临床意义
    'discussion_implications': """
撰写临床意义（150-200字）：

主题：{topic}
证据质量：{evidence_quality}
主要发现：{key_findings}

要求：
1. 临床实践意义
2. 医疗提供者建议
3. 实施考虑因素
4. 患者获益评估

撰写临床意义：
""",

    # 结论-证据总结
    'conclusion_evidence': """
撰写结论-证据总结（100-150字）：

主题：{topic}
研究：{study_count}项，{participants}人
证据质量：{evidence_quality}

要求：
1. 重述重要发现
2. 证据质量等级
3. 效应量和置信区间
4. 严格基于证据

撰写证据总结：
""",

    # 结论-临床建议
    'conclusion_recommendations': """
撰写临床建议（80-120字）：

主题：{topic}
证据强度：{evidence_quality}
临床效果：{clinical_effect}

要求：
1. 临床有效性陈述
2. 实用建议
3. 推荐强度
4. 谨慎语言

撰写建议：
"""
}

# 优化的英文提示词 - 针对小模型上下文限制
OPTIMIZED_PROMPTS_EN = {
    
    # Professional title generation
    'generate_professional_title': """
EBM expert. Generate professional title based on real data:

Topic: {topic}
Studies: {study_count}
Designs: {main_designs}
Participants: {participants}

Requirements:
1. Include PICO elements
2. Add "Systematic Review and Meta-Analysis"
3. Within 20 words
4. Standard medical terminology

Format: [Intervention] vs [Comparison] for [Condition]: Systematic Review and Meta-Analysis

Generate title:
""",

    # Abstract background - compressed
    'abstract_background': """
Write background for systematic review abstract (80-120 words):

Topic: {topic}
Studies: {study_count}
Designs: {designs}

Requirements:
1. Clinical importance
2. Current treatment limitations
3. Need for this review
4. Evidence-based

Write background:
""",

    # Abstract objective - concise version
    'abstract_objective': """
Write systematic review objective (50-80 words):

Topic: {topic}
Interventions: {interventions}
Population: {population}

Requirements:
1. Use PICO framework
2. Primary outcomes
3. Precise language

Write objective:
""",

    # Abstract methods - core elements
    'abstract_methods': """
Write systematic review methods (100-150 words):

Studies: {study_count}
Designs: {designs}
Quality: {quality_info}

Requirements:
1. Search strategy and databases
2. Inclusion/exclusion criteria
3. Quality assessment (ROB, GRADE)
4. Data extraction/analysis

Write methods:
""",

    # Abstract results - data-driven
    'abstract_results': """
Write systematic review results (120-180 words):

Studies: {study_count}
Participants: {participants}
Design distribution: {design_dist}
Sample size range: {sample_range}

Requirements:
1. Number of studies and participants
2. Main outcome results
3. Heterogeneity analysis
4. Specific statistics

Write results:
""",

    # Abstract conclusion - evidence-based
    'abstract_conclusion': """
Write systematic review conclusion (80-120 words):

Topic: {topic}
Evidence quality: {evidence_quality}
Studies: {study_count}

Requirements:
1. Answer research question
2. Evidence quality level
3. Clinical recommendations
4. Future research directions

Write conclusion:
""",

    # Introduction - background section
    'introduction_background': """
Write introduction background (200-300 words):

Topic: {topic}
Data: {study_count} studies, {participants} participants
Publication years: {year_range}

Requirements:
1. Clinical significance of {topic}
2. Current standard treatments and limitations
3. Based on real medical evidence
4. Accurate terminology

Write background:
""",

    # Introduction rationale
    'introduction_rationale': """
Write introduction rationale (100-150 words):

Topic: {topic}
Existing studies: {study_count}
Quality distribution: {quality_dist}

Requirements:
1. Specific knowledge gaps
2. Limitations of current evidence
3. How this review advances the field
4. Clear logic

Write rationale:
""",

    # Introduction objectives
    'introduction_objectives': """
Write introduction objectives (100-150 words):

Topic: {topic}
Population: {populations}
Interventions: {interventions}

Requirements:
1. Primary PICO question
2. Secondary objectives
3. Measurable outcomes
4. Specific and clear

Write objectives:
""",

    # Methods - study protocol
    'methods_protocol': """
Write methods - study protocol (150-200 words):

Study type: Systematic Review and Meta-analysis
Registration: {registration_info}

Requirements:
1. Protocol development
2. Public registration (PROSPERO, etc.)
3. PRISMA 2020 compliance
4. Transparency and reproducibility

Write protocol:
""",

    # Methods - eligibility criteria
    'methods_eligibility': """
Write inclusion/exclusion criteria (200-250 words):

Data basis:
- Studies: {study_count}
- Designs: {designs}
- Population: {populations}
- Interventions: {interventions}

Requirements:
1. Detailed PICOS criteria
2. Population, intervention, comparison, outcomes, study design
3. Based on included studies
4. Clear and operational criteria

Write criteria:
""",

    # Methods - search strategy
    'methods_search': """
Write search strategy (150-200 words):

Results: {study_count} studies
Timeframe: {year_range}
Languages: English and Chinese

Requirements:
1. Electronic databases (PubMed, EMBASE, Cochrane, CNKI)
2. Date and language limits
3. Other sources (grey literature, conference abstracts)
4. Transparent search strategy

Write search strategy:
""",

    # Methods - quality assessment
    'methods_quality': """
Write quality assessment methods (150-200 words):

Quality distribution: {quality_dist}
Tools: ROB tools, GRADE system

Requirements:
1. Rationale for tool selection
2. Assessment process (independent dual review)
3. Disagreement resolution
4. GRADE evidence rating

Write quality assessment:
""",

    # Results - study characteristics
    'results_characteristics': """
Write study characteristics (200-300 words):

Included studies: {study_count}
Participants: {participants}
Design distribution: {design_dist}
Year range: {year_range}
Sample sizes: {sample_range}

Requirements:
1. Basic study characteristics
2. Participant demographics
3. Intervention details
4. Outcome measures and follow-up
5. Objective description of data

Write characteristics:
""",

    # Results - quality assessment
    'results_quality': """
Write quality assessment results (150-200 words):

Quality distribution: {quality_dist}
Risk of bias: Based on ROB assessment
GRADE rating: {grade_summary}

Requirements:
1. Risk of bias across studies
2. Overall quality assessment
3. GRADE certainty of evidence
4. Use of tables/figures

Write quality assessment:
""",

    # Results - main findings
    'results_main_findings': """
Write main results (200-300 words):

Data: {study_count} studies, {participants} participants
Main outcomes: Based on pooled analysis
Heterogeneity: {heterogeneity_info}

Requirements:
1. Main outcome pooled results
2. Effect sizes and confidence intervals
3. Heterogeneity testing
4. Forest plot description
5. Specific statistics

Write main results:
""",

    # Discussion - main findings
    'discussion_findings': """
Write discussion - main findings (200-300 words):

Topic: {topic}
Evidence: {study_count} studies, {participants} participants
Quality: {evidence_quality}

Requirements:
1. Summary of key findings
2. Strength and direction of evidence
3. Answer to research question
4. Based on real data analysis

Write main findings:
""",

    # Discussion - limitations
    'discussion_limitations': """
Write discussion - limitations (150-200 words):

Study quality: {quality_dist}
Heterogeneity: {heterogeneity_info}
Risk of bias: {bias_summary}

Requirements:
1. Limitations of included studies
2. Methodological limitations
3. Impact of heterogeneity
4. Publication bias potential
5. Objective and candid

Write limitations:
""",

    # Discussion - clinical implications
    'discussion_implications': """
Write clinical implications (150-200 words):

Topic: {topic}
Evidence quality: {evidence_quality}
Key findings: {key_findings}

Requirements:
1. Implications for clinical practice
2. Recommendations for healthcare providers
3. Implementation considerations
4. Patient benefit assessment

Write clinical implications:
""",

    # Conclusion - evidence summary
    'conclusion_evidence': """
Write conclusion - evidence summary (100-150 words):

Topic: {topic}
Studies: {study_count}, {participants} participants
Evidence quality: {evidence_quality}

Requirements:
1. Restate key findings
2. Evidence quality level
3. Effect sizes and CIs
4. Strictly evidence-based

Write evidence summary:
""",

    # Conclusion - clinical recommendations
    'conclusion_recommendations': """
Write clinical recommendations (80-120 words):

Topic: {topic}
Evidence strength: {evidence_quality}
Clinical effect: {clinical_effect}

Requirements:
1. Clinical effectiveness statement
2. Practical recommendations
3. Strength of recommendation
4. Cautious language

Write recommendations:
"""
}
