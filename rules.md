/ebm_review_pro
|
|-- docker-compose.yml        # 一键启动整个项目 (后端, 前端, 任务队列, 缓存)
|
|-- backend/                    # FastAPI 后端 & Celery 任务
|   |-- Dockerfile
|   |-- requirements.txt
|   |-- app/
|   |   |-- api/                # API 路由 (endpoints)
|   |   |   |-- routes.py
|   |   |-- core/               # 核心配置和设置
|   |   |   |-- config.py
|   |   |-- services/           # 核心业务逻辑
|   |   |   |-- literature_search.py   # (新) 多源文献检索模块
|   |   |   |-- ebm_generator.py       # (升级) 核心生成器
|   |   |   |-- prompts.py             # (新) 中英文提示词库
|   |   |-- tasks/              # Celery 异步任务
|   |   |   |-- generation_task.py
|   |   |-- main.py             # FastAPI 应用入口
|   |-- celery_worker.py        # Celery worker 启动脚本
|
|-- frontend/                   # Vue.js 前端
|   |-- index.html              # 主 HTML 文件
|   |-- main.js                 # Vue 应用逻辑
|   |-- style.css               # 样式表
|
|-- README.md                   # 项目说明